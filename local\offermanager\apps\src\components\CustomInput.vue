<template>
  <div class="custom-input-container" :style="customWidth">
    <div v-if="label" class="input-label">{{ label }}</div>
    <div class="input-wrapper" :class="{ 'with-icon': hasSearchIcon || isDateType }">
      <input
        :type="type"
        :placeholder="placeholder"
        :value="modelValue"
        @input="handleInput"
        @blur="handleBlur"
        :disabled="disabled"
        class="form-control custom-input"
        :class="{ 'error': hasError }"
        :min="isNumberType ? 0 : null"
        :max="max"
      >
      <div v-if="hasSearchIcon" class="search-icon">
        <i class="fas fa-search"></i>
      </div>
      <div v-if="isDateType" class="calendar-icon" :class="{ 'disabled': disabled }">
        <i class="fas fa-calendar-alt"></i>
      </div>
    </div>
    <div v-if="hasError && errorMessage" class="error-message">{{ errorMessage }}</div>
  </div>
</template>

<script>
export default {
  name: 'CustomInput',

  props: {
    modelValue: {
      type: [String, Number],
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: 'Digite aqui...'
    },
    type: {
      type: String,
      default: 'text'
    },
    hasSearchIcon: {
      type: Boolean,
      default: false
    },
    width: {
      type: [String, Number],
      default: null
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hasError: {
      type: Boolean,
      default: false
    },
    errorMessage: {
      type: String,
      default: ''
    },
    required: {
      type: Boolean,
      default: false
    },
    max: {
      type: [String, Number],
      default: null
    }
  },

  computed: {
    customWidth() {
      return this.width ? { width: typeof this.width === 'number' ? `${this.width}px` : this.width } : {}
    },
    isDateType() {
      return this.type === 'date'
    },
    isNumberType() {
      return this.type === 'number'
    }
  },

  methods: {
    handleInput(event) {
      let value = event.target.value;

      // Para campos numéricos, impedir valores negativos e validar valor máximo
      if (this.isNumberType) {
        // Remover qualquer sinal negativo do valor
        if (value.includes('-')) {
          value = value.replace(/-/g, '');
          event.target.value = value;
        }

        // Se o valor não estiver vazio, garantir que seja positivo e não exceda o máximo
        if (value !== '') {
          // Converter para número e verificar se é negativo
          const numValue = parseFloat(value);
          if (numValue < 0 || isNaN(numValue)) {
            // Se for negativo ou não for um número válido, definir como vazio
            value = '';
            event.target.value = value;
          }
          // Verificar se excede o valor máximo
          else if (this.max !== null && numValue > parseFloat(this.max)) {
            // Se exceder o valor máximo, definir como o valor máximo
            value = this.max.toString();
            event.target.value = value;
            // Emitir evento de validação para atualizar mensagens de erro
            this.$emit('validate');
          }
        }
      }

      this.$emit('update:modelValue', value);

      // Se o campo estava com erro e agora tem valor, emitir evento de validação
      if (this.hasError && value) {
        this.$emit('validate');
      }
    },

    handleBlur(event) {
      // Quando o campo perde o foco, emitir evento de validação
      if (this.required) {
        this.$emit('validate');
      }
    }
  },

  emits: ['update:modelValue', 'validate']
}
</script>

<style lang="scss" scoped>
.custom-input-container {
  width: 100%;

  @media (max-width: 768px) {
    width: 100% !important;
  }
}

.input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: 500;
  color: #fff !important;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;

  &.with-icon .custom-input {
    border-radius: 4px 0 0 4px;
    border-right: none;
  }

  .calendar-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    pointer-events: none;

    &.disabled {
      opacity: 0.65;
    }
  }
}

.form-control {
  border-radius: 4px;
}

.custom-input {
  width: 100%;
  max-width: 336px;
  padding: 8px 10px;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #fff !important;
  background-color: #212529 !important;
  border: 1px solid #495057 !important;
  border-radius: 4px;
  height: 38px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  /* Remove as setas de incremento/decremento para campos numéricos */
  &[type="number"] {
    -webkit-appearance: textfield !important;
    -moz-appearance: textfield !important;
    appearance: textfield !important;

    /* Para Firefox */
    -moz-appearance: textfield !important;

    /* Para Chrome, Safari, Edge, Opera */
    &::-webkit-outer-spin-button,
    &::-webkit-inner-spin-button {
      -webkit-appearance: none !important;
      margin: 0 !important;
      display: none !important;
    }
  }

  &:focus {
    outline: 0;
    border-color: var(--primary) !important;
  }

  &::placeholder {
    color: #6c757d !important;
  }

  &:disabled {
    opacity: 0.65;
    cursor: not-allowed;
  }

  &.error {
    border-color: #dc3545 !important;

    &:focus {
      box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    }
  }
}

.search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
  font-weight: 500;
}
</style>